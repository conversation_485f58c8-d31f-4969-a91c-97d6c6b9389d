
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 55 9% 21%; /* Updated to match header color #37372f */
    --primary-foreground: 0 0% 100%; /* White text for contrast */

    --secondary: 55 9% 31%; /* Slightly lighter version of header color #4a4a3f */
    --secondary-foreground: 0 0% 100%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 55 9% 21%; /* Updated to match header color */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 55 9% 21%; /* Updated to match header color */

    --radius: 0.5rem;

    /* Mobile specific values */
    --sidebar-width: 16rem;
    --sidebar-width-icon: 3rem;

    /* Set sidebar background color for light mode */
    --sidebar-background: 0 0% 100%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 55 9% 21%; /* Updated to match header color #37372f */
    --primary-foreground: 210 40% 98%;

    --secondary: 55 9% 31%; /* Slightly lighter version of header color #4a4a3f */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 55 9% 21%; /* Updated to match header color */
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 55 9% 21%; /* Updated to match header color */

    /* Set sidebar background color for dark mode */
    --sidebar-background: 222.2 84% 4.9%;
  }

  /* Neon Theme - Cyberpunk inspired with bright accents */
  .theme-neon {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 267 75% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 326 100% 60%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 10% 10%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 326 100% 60%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 10% 10%;
    --input: 240 10% 10%;
    --ring: 267 75% 60%;
  }

  /* Midnight Theme - Deep blue hues with subtle highlights */
  .theme-midnight {
    --background: 220 65% 5%;
    --foreground: 210 40% 98%;

    --card: 220 65% 7%;
    --card-foreground: 210 40% 98%;

    --popover: 220 65% 7%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 76% 56%;
    --primary-foreground: 0 0% 100%;

    --secondary: 185 70% 52%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 65% 12%;
    --muted-foreground: 215 20% 70%;

    --accent: 185 70% 52%;
    --accent-foreground: 220 65% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 65% 15%;
    --input: 220 65% 15%;
    --ring: 217 76% 56%;
  }

  /* Oceanic Theme - Soft aquatic palette with teal accents */
  .theme-oceanic {
    --background: 200 30% 98%;
    --foreground: 200 50% 20%;

    --card: 200 35% 96%;
    --card-foreground: 200 50% 20%;

    --popover: 200 35% 96%;
    --popover-foreground: 200 50% 20%;

    --primary: 180 50% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 200 65% 60%;
    --secondary-foreground: 0 0% 100%;

    --muted: 200 25% 90%;
    --muted-foreground: 200 30% 40%;

    --accent: 200 65% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 200 25% 90%;
    --input: 200 25% 90%;
    --ring: 180 50% 45%;
  }

  /* Cyborg Theme - High-contrast industrial theme with red accents */
  .theme-cyborg {
    --background: 0 0% 7%;
    --foreground: 0 0% 95%;

    --card: 0 0% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 80% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 30 80% 50%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 30 80% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 80% 50%;
  }

  /* Aurora Theme - Northern lights inspired with gradient feel */
  .theme-aurora {
    --background: 260 50% 8%;
    --foreground: 260 10% 95%;

    --card: 260 50% 10%;
    --card-foreground: 260 10% 95%;

    --popover: 260 50% 10%;
    --popover-foreground: 260 10% 95%;

    --primary: 140 70% 50%;
    --primary-foreground: 260 50% 10%;

    --secondary: 280 70% 60%;
    --secondary-foreground: 260 10% 95%;

    --muted: 260 50% 15%;
    --muted-foreground: 260 10% 70%;

    --accent: 280 70% 60%;
    --accent-foreground: 260 10% 95%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 260 50% 15%;
    --input: 260 50% 15%;
    --ring: 140 70% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-poppins font-light;
    -webkit-tap-highlight-color: transparent;
  }

  /* Add responsive font sizing */
  h1 {
    @apply text-xl md:text-3xl;
  }

  h2 {
    @apply text-lg md:text-2xl;
  }

  h3 {
    @apply text-base md:text-xl;
  }

  p {
    @apply text-sm md:text-base;
  }

  /* Improve tap target sizes on mobile */
  button, a, input, select, textarea {
    @apply min-h-[40px] touch-manipulation;
  }
}

/* Button specific styles */
.btn {
  @apply rounded-md font-medium transition-colors shadow-sm;
}

.btn-primary {
  @apply bg-gradient-to-r from-primary to-secondary text-primary-foreground hover:shadow-md transition-all;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

.btn-outline {
  @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
}

.btn-ghost {
  @apply hover:bg-accent hover:text-accent-foreground;
}

/* Gradient button styles */
.btn-gradient {
  @apply bg-gradient-to-r from-primary to-secondary text-primary-foreground border-none shadow-md hover:shadow-lg transition-all duration-200;
}

/* Toggle button styles */
.toggle-gradient {
  @apply relative overflow-hidden transition-all duration-200;
}

.toggle-gradient[data-state="on"] {
  @apply bg-gradient-to-r from-primary to-secondary text-primary-foreground;
}

.toggle-gradient[data-state="off"] {
  @apply bg-muted/30;
}

/* Modern toggle switch styles */
.toggle-modern {
  @apply inline-flex h-10 w-20 cursor-pointer items-center rounded-full bg-muted p-1 shadow-inner transition-all duration-300;
}

.toggle-modern-thumb {
  @apply flex h-8 w-8 transform items-center justify-center rounded-full transition-transform duration-300;
}

.toggle-modern-thumb.active {
  @apply translate-x-10 bg-primary text-primary-foreground;
}

.toggle-modern-thumb.inactive {
  @apply bg-muted-foreground/20;
}

/* Switch styles */
.switch-gradient {
  @apply bg-gradient-to-r from-primary to-secondary;
}

/* Theme-specific styles */
.theme-neon .card {
  @apply border-purple-500/20 bg-gradient-to-br from-background to-black/40;
}

.theme-neon button.primary {
  @apply bg-gradient-to-r from-purple-600 to-pink-500;
}

.theme-midnight .card {
  @apply border-blue-500/20 bg-gradient-to-br from-background to-blue-950;
}

.theme-oceanic .card {
  @apply border-teal-500/20 bg-gradient-to-br from-background to-cyan-50;
}

.theme-cyborg .card {
  @apply border-red-500/10 bg-gradient-to-br from-background to-zinc-900;
}

.theme-aurora .card {
  @apply border-green-500/20 bg-gradient-to-br from-background to-purple-950;
}

/* Theme transition effects */
.theme-transition {
  @apply transition-colors duration-300 ease-in-out;
}

/* Form transition animations */
.form-step {
  @apply opacity-0 translate-x-4 transition-all duration-300 ease-in-out;
}

.form-step.active {
  @apply opacity-100 translate-x-0;
}

.form-step.previous {
  @apply -translate-x-4;
}

/* Progress bar styling */
.progress-bar {
  @apply h-1 bg-muted overflow-hidden rounded-full;
}

.progress-bar-fill {
  @apply h-full bg-primary transition-all duration-300 ease-in-out;
}

/* Step indicators with responsive sizing */
.step-indicator {
  @apply flex items-center justify-center w-6 h-6 md:w-8 md:h-8 rounded-full text-xs font-medium transition-all duration-200;
}

.step-indicator.completed {
  @apply bg-primary text-primary-foreground;
}

.step-indicator.current {
  @apply bg-accent text-accent-foreground;
}

.step-indicator.upcoming {
  @apply bg-muted text-muted-foreground;
}

.step-connector {
  @apply h-0.5 flex-grow mx-0.5 md:mx-1 bg-muted transition-all duration-200;
}

.step-connector.completed {
  @apply bg-primary;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(10px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Make completed or current steps look clickable */
.step-indicator.completed,
.step-indicator.current {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.step-indicator.completed:hover,
.step-indicator.current:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  .step-indicator {
    @apply w-6 h-6 text-[10px];
  }

  .step-connector {
    @apply mx-0.5;
  }

  form {
    @apply space-y-4;
  }

  /* Fix scrolling issues on mobile */
  html, body {
    @apply overflow-x-hidden;
  }

  /* Hide scrollbars but keep scroll functionality for cleaner mobile UI */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }

  /* Improved touch targets for mobile */
  button, a, input, select {
    @apply min-h-[40px] touch-manipulation;
  }

  /* Dialog/modal improvements for mobile */
  .DialogContent {
    @apply w-[95vw] max-w-full p-4;
  }

  /* Table improvements for mobile */
  table {
    @apply text-xs;
  }

  /* Card improvements for mobile */
  .card {
    @apply px-3 py-4;
  }
}

/* Fix for sidebar on mobile */
[data-sidebar="sidebar"][data-mobile="true"] {
  @apply w-[85vw] max-w-[300px] bg-background;
}

/* Mobile sidebar - ensure solid background */
.radix-sheet-content {
  @apply bg-background !important;
}

/* Override SheetContent for mobile sidebar to ensure solid background */
[data-state=open].fixed.z-50[role="dialog"] {
  background-color: hsl(var(--background));
}

/* Fix navigation menu dropdown on mobile */
@media (max-width: 768px) {
  [data-radix-popper-content-wrapper] {
    @apply max-w-[92vw] !important;
  }
}

/* Improve touch targets for interactive elements */
.interactive-target {
  @apply min-h-[44px] min-w-[44px];
}

/* Safe area insets for modern mobile browsers */
.safe-area-inset {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Scrollbar improvements for touch devices */
@media (pointer: coarse) {
  ::-webkit-scrollbar {
    @apply w-1;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
}

/* Button consistency improvements */
button.shadcn-button {
  @apply flex items-center justify-center gap-2 rounded-md font-medium transition-colors shadow-sm;
}

/* Safari Calendar Fix - Prevent height inconsistencies when switching months */
.safari-calendar-fix {
  @apply min-h-[320px] max-h-[320px];
  /* Ensure consistent dimensions across all browsers */
  width: 280px !important;
  height: 320px !important;
  overflow: hidden;
}

/* Safari-specific fixes for calendar popover */
@supports (-webkit-appearance: none) {
  .safari-calendar-fix {
    /* Force consistent layout in Safari */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    /* Prevent Safari from adjusting height based on content */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Fix calendar table layout in Safari */
  .safari-calendar-fix table {
    table-layout: fixed !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Ensure month navigation doesn't affect height */
  .safari-calendar-fix .rdp-caption {
    height: 40px !important;
    min-height: 40px !important;
    max-height: 40px !important;
  }

  /* Fix day cells to prevent layout shifts */
  .safari-calendar-fix .rdp-cell {
    height: 36px !important;
    width: 36px !important;
  }
}

/* Additional popover positioning fixes for Safari */
[data-radix-popper-content-wrapper] {
  /* Prevent popover from repositioning when content changes */
  position: fixed !important;
}

/* Ensure popover content maintains consistent size */
[data-radix-popover-content] {
  /* Prevent automatic resizing */
  max-width: none !important;
  max-height: none !important;
}

/* Safari-specific popover fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  [data-radix-popover-content]:has(.safari-calendar-fix) {
    /* Lock dimensions for Safari */
    width: 280px !important;
    height: auto !important;
    min-height: 320px !important;
    /* Prevent content overflow issues */
    overflow: visible !important;
  }
}
