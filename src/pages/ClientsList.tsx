import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, FileText, Trash2, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { getWalkthroughs, deleteWalkthrough } from '@/services/walkthroughService';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';

const ClientsList = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const isMobile = useIsMobile();

  const { data: walkthroughs, isLoading, refetch } = useQuery({
    queryKey: ['walkthroughs'],
    queryFn: getWalkthroughs,
  });

  const handleDeleteClick = (id: string) => {
    setDeleteId(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!deleteId) return;

    try {
      const success = await deleteWalkthrough(deleteId);
      if (success) {
        toast({
          title: "Success",
          description: "Client walkthrough deleted successfully",
        });
        refetch();
      } else {
        throw new Error("Failed to delete walkthrough");
      }
    } catch (error) {
      console.error("Error deleting walkthrough:", error);
      toast({
        title: "Error",
        description: "Failed to delete the walkthrough",
        variant: "destructive",
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setDeleteId(null);
    }
  };

  const filteredClients = walkthroughs?.filter(client =>
    client.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    client.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate progress percentage function
  const calculateProgress = (lastStep: number) => {
    // The walkthrough has 7 total steps (1-7)
    const totalSteps = 7;
    // Ensure progress doesn't exceed 100%
    return Math.min(Math.round((lastStep / totalSteps) * 100), 100);
  };

  return (
    <div className="container mx-auto py-4 md:py-8 px-3 md:px-4">
      <div className="animate-fade-in">
        <div className="flex items-center justify-between mb-4 md:mb-6 flex-wrap gap-3">
          <div className="flex items-center">
            <Users className="h-5 w-5 md:h-6 md:w-6 mr-2" />
            <h1 className="text-xl md:text-2xl font-bold">Clients List</h1>
          </div>
          <Button onClick={() => navigate('/new-walkthrough')} size={isMobile ? "sm" : "default"}>
            <Plus className="h-4 w-4 mr-1" />
            {isMobile ? "New Client" : "New Client Walkthrough"}
          </Button>
        </div>

        <div className="bg-card rounded-lg shadow p-3 md:p-6 mb-4 md:mb-6">
          <div className="flex justify-between mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clients..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-6 md:py-8">
              <p className="text-sm md:text-base">Loading clients...</p>
            </div>
          ) : filteredClients && filteredClients.length > 0 ? (
            <div className="overflow-x-auto -mx-3 md:mx-0">
              {isMobile ? (
                <div className="space-y-4 px-3">
                  {filteredClients.map((client) => (
                    <Card key={client.id} className="overflow-hidden border">
                      <CardContent className="p-3 md:p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium text-sm md:text-base truncate max-w-[180px]">{client.client_name}</h3>
                          <span className="text-xs text-muted-foreground whitespace-nowrap">
                            {format(new Date(client.updated_at), 'MMM d, yyyy')}
                          </span>
                        </div>
                        <p className="text-xs md:text-sm mb-3 line-clamp-1 text-muted-foreground">{client.title}</p>
                        <div className="mb-3">
                          <div className="flex items-center">
                            <div className="w-full bg-muted rounded-full h-2 mr-2">
                              <div
                                className="bg-primary h-2 rounded-full"
                                style={{ width: `${calculateProgress(client.last_step)}%` }}
                              />
                            </div>
                            <span className="text-xs">{calculateProgress(client.last_step)}%</span>
                          </div>
                        </div>
                        <div className="flex gap-1 justify-end mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-2 text-xs"
                            onClick={() => navigate(`/edit-walkthrough/${client.id}`)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-2 text-xs"
                            onClick={() => navigate(`/reports?clientId=${client.id}`)}
                          >
                            <FileText className="h-3.5 w-3.5" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-2 text-xs"
                            onClick={() => handleDeleteClick(client.id)}
                          >
                            <Trash2 className="h-3.5 w-3.5 text-destructive" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Client Name</TableHead>
                      <TableHead>Project Title</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredClients.map((client) => (
                      <TableRow key={client.id}>
                        <TableCell className="font-medium">{client.client_name}</TableCell>
                        <TableCell>{client.title}</TableCell>
                        <TableCell>{format(new Date(client.updated_at), 'MMM d, yyyy')}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <div className="w-full bg-muted rounded-full h-2 mr-2">
                              <div
                                className="bg-primary h-2 rounded-full"
                                style={{ width: `${calculateProgress(client.last_step)}%` }}
                              />
                            </div>
                            <span className="text-xs">{calculateProgress(client.last_step)}%</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/edit-walkthrough/${client.id}`)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/reports?clientId=${client.id}`)}
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteClick(client.id)}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          ) : (
            <div className="text-center py-6 md:py-8">
              <p className="text-sm md:text-base text-muted-foreground">No clients found</p>
              {searchQuery ? (
                <p className="mt-2 text-xs md:text-sm text-muted-foreground">Try adjusting your search criteria</p>
              ) : (
                <Button
                  variant="outline"
                  className="mt-4"
                  size={isMobile ? "sm" : "default"}
                  onClick={() => navigate('/new-walkthrough')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add your first client
                </Button>
              )}
            </div>
          )}
        </div>

        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Delete Client Walkthrough</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this client walkthrough? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0 mt-4">
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={confirmDelete}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ClientsList;
