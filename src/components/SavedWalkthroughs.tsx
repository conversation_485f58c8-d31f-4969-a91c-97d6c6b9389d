
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { FolderOpen, Trash2, Clock, RefreshCw } from 'lucide-react';
import { getWalkthroughs, deleteWalkthrough, Walkthrough } from '@/services/walkthroughService';
import { useFormContext } from '../context/FormContext';

interface SavedWalkthroughsProps {
  onLoad: (id: string) => void;
}

const SavedWalkthroughs: React.FC<SavedWalkthroughsProps> = ({
  onLoad
}) => {
  const {
    toast
  } = useToast();
  const [walkthroughs, setWalkthroughs] = useState<Walkthrough[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();

  const loadWalkthroughs = async () => {
    setIsLoading(true);
    try {
      const data = await getWalkthroughs();
      setWalkthroughs(data);
    } catch (error) {
      console.error("Error loading walkthroughs:", error);
      toast({
        title: "Error",
        description: "Failed to load saved walkthroughs",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      loadWalkthroughs();
    }
  }, [open]);

  const handleDelete = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const success = await deleteWalkthrough(id);
      if (success) {
        toast({
          title: "Success",
          description: "Walkthrough deleted successfully"
        });

        setWalkthroughs(walkthroughs.filter(w => w.id !== id));
      } else {
        throw new Error("Failed to delete walkthrough");
      }
    } catch (error) {
      console.error("Error deleting walkthrough:", error);
      toast({
        title: "Error",
        description: "Failed to delete walkthrough",
        variant: "destructive"
      });
    }
  };

  const handleLoad = (walkthrough: Walkthrough) => {
    onLoad(walkthrough.id);
    setOpen(false);
  };

  return <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" size="sm" className="gap-2 text-white bg-[#37372f] hover:bg-[#4a4a3f]">
          <FolderOpen size={16} />
          Resume Saved
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Saved Walkthroughs</DialogTitle>
          <DialogDescription>
            Select a saved walkthrough to resume or start a new one.
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-end mb-4">
          <Button variant="outline" size="sm" onClick={loadWalkthroughs} disabled={isLoading} className="gap-2">
            <RefreshCw size={16} className={isLoading ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>

        {isLoading ? <div className="flex justify-center items-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div> : walkthroughs.length === 0 ? <div className="text-center py-8 text-muted-foreground">
            No saved walkthroughs found.
          </div> : <div className="grid gap-4">
            {walkthroughs.map(walkthrough => {
              // Safely get client name and other properties with fallbacks
              const clientName = walkthrough.client_name || "Unnamed Client";
              const projectType = walkthrough.form_data?.clientInfo?.projectType || "Unknown";
              const squareFootage = walkthrough.form_data?.qualificationInfo?.squareFootage || 0;
              const lastStep = walkthrough.last_step !== undefined ? walkthrough.last_step : 1;

              return (
                <Card key={walkthrough.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => handleLoad(walkthrough)}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle>{walkthrough.title}</CardTitle>
                      <Button variant="ghost" size="icon" onClick={e => handleDelete(walkthrough.id, e)} className="h-8 w-8">
                        <Trash2 size={16} className="text-destructive" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                    <CardDescription>
                      {clientName}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2 pt-0">
                    <div className="text-sm flex items-center gap-1 text-muted-foreground">
                      <Clock size={14} />
                      Last updated: {format(new Date(walkthrough.updated_at), 'PPp')}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="text-xs text-muted-foreground">
                      {projectType} •
                      {squareFootage.toLocaleString()} sq ft •
                      Step {lastStep} of 7
                    </div>
                  </CardFooter>
                </Card>
              );
            })}
          </div>}
      </DialogContent>
    </Dialog>;
};

export default SavedWalkthroughs;
