
import React, { useState, useRef } from 'react';
import { useFormContext } from '../../context/FormContext';
import FormNavigation from '../FormNavigation';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Upload, FileX } from 'lucide-react';

const FilesUploadStep: React.FC = () => {
  const { formData, setFormData } = useFormContext();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files?.length) return;

    const newFiles = Array.from(files);
    setUploadedFiles(prev => [...prev, ...newFiles]);
    
    // Add files to formData if needed
    setFormData(prev => ({
      ...prev,
      projectFiles: [...(prev.projectFiles || []), ...newFiles]
    }));
    
    // Clear the input value so the same file can be uploaded again if needed
    e.target.value = '';
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    setFormData(prev => ({
      ...prev,
      projectFiles: (prev.projectFiles || []).filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-6">Project Files</h2>
      <p className="text-muted-foreground mb-8">
        Upload any relevant project files, documents, or plans.
      </p>

      <div className="space-y-8">
        <div className="bg-muted/30 rounded-lg p-6">
          <div className="text-center space-y-4">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
            />
            
            <Button 
              variant="outline" 
              onClick={triggerFileInput}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Choose Files
            </Button>
            
            <p className="text-sm text-muted-foreground">
              Upload any relevant documents, plans, or diagrams
            </p>
          </div>
          
          {uploadedFiles.length > 0 && (
            <div className="mt-6">
              <Label className="mb-2 block">Uploaded Files ({uploadedFiles.length})</Label>
              <div className="space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div key={index} className="flex justify-between items-center bg-background p-2 rounded border">
                    <span className="text-sm truncate max-w-[80%]">{file.name}</span>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => removeFile(index)}
                      className="h-8 w-8 p-0"
                    >
                      <FileX className="h-4 w-4" />
                      <span className="sr-only">Remove file</span>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <FormNavigation />
    </div>
  );
};

export default FilesUploadStep;
