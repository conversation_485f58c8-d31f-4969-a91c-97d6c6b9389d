
import { FormData } from "../types/formTypes";

export interface Walkthrough {
  id: string;
  title: string;
  client_name: string;
  form_data: FormData;
  last_step: number;
  created_at: string;
  updated_at: string;
}

// Get a specific walkthrough by ID
export const getWalkthroughById = async (id: string): Promise<Walkthrough | null> => {
  try {
    // In a real implementation, this would call an API endpoint
    const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    const walkthrough = walkthroughs.find((w: Walkthrough) => w.id === id);
    return walkthrough || null;
  } catch (error) {
    console.error("Error getting walkthrough by ID:", error);
    return null;
  }
};

// Export getWalkthroughById as getWalkthrough for backward compatibility
export const getWalkthrough = getWalkthroughById;

// Get all walkthroughs
export const getWalkthroughs = async (): Promise<Walkthrough[]> => {
  try {
    // In a real implementation, this would call an API endpoint
    const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    return walkthroughs;
  } catch (error) {
    console.error("Error getting walkthroughs:", error);
    return [];
  }
};

// Save a new walkthrough
export const saveWalkthrough = async (title: string, formData: FormData): Promise<string | null> => {
  try {
    const id = `walkthrough-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newWalkthrough: Walkthrough = {
      id,
      title,
      client_name: formData.clientInfo.fullName || "Unnamed Client",
      form_data: formData,
      last_step: formData.currentStep,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    walkthroughs.push(newWalkthrough);
    localStorage.setItem('walkthroughs', JSON.stringify(walkthroughs));
    
    return id;
  } catch (error) {
    console.error("Error saving walkthrough:", error);
    return null;
  }
};

// Update an existing walkthrough
export const updateWalkthrough = async (id: string, title: string, formData: FormData): Promise<boolean> => {
  try {
    const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    const index = walkthroughs.findIndex((w: Walkthrough) => w.id === id);
    
    if (index === -1) {
      return false;
    }
    
    walkthroughs[index] = {
      ...walkthroughs[index],
      title,
      client_name: formData.clientInfo.fullName || walkthroughs[index].client_name,
      form_data: formData,
      last_step: formData.currentStep,
      updated_at: new Date().toISOString(),
    };
    
    localStorage.setItem('walkthroughs', JSON.stringify(walkthroughs));
    return true;
  } catch (error) {
    console.error("Error updating walkthrough:", error);
    return false;
  }
};

// Delete a walkthrough
export const deleteWalkthrough = async (id: string): Promise<boolean> => {
  try {
    const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    const filteredWalkthroughs = walkthroughs.filter((w: Walkthrough) => w.id !== id);
    
    localStorage.setItem('walkthroughs', JSON.stringify(filteredWalkthroughs));
    return true;
  } catch (error) {
    console.error("Error deleting walkthrough:", error);
    return false;
  }
};
